package com.snct.utils;

import com.snct.common.config.SnctConfig;
import com.snct.system.domain.Device;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 设备原始数据文件保存工具类
 *
 * 提供设备原始数据的文件保存功能，支持按设备编码和日期分类存储
 * 支持清理过期的原始数据文件
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public class DeviceRawDataFileUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(DeviceRawDataFileUtil.class);
    
    /**
     * 保存原始数据到本地文件
     * 根据设备编码和日期分类，保存为txt文本格式，包含采集时间和原始数据
     *
     * @param device    设备对象
     * @param rawData   原始数据
     * @param timestamp 采集时间戳
     */
    public static void saveRawDataToFile(Device device, String rawData, Long timestamp) {
        if (device == null || StringUtils.isBlank(device.getCode()) || StringUtils.isBlank(rawData)) {
            return;
        }

        try {
            String deviceCode = device.getCode();
            Date date = new Date(timestamp);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

            String dateStr = dateFormat.format(date);
            String timeStr = timeFormat.format(date);

            // 构建文件路径：基础路径/原始数据/设备编码/
            String basePath = SnctConfig.getProfile();
            String dirPath =  File.separator + "snct" + File.separator + "device_raw_data" + File.separator + dateStr;

            // 创建目录
            File dir = new File(dirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 构建文件名：设备编码_年月日.txt
            String fileName = deviceCode + "_" + dateStr + ".txt";
            File file = new File(dir, fileName);

            // 构建文件内容：[时间戳] 原始数据内容
            String content = "[" + timeStr + "] " + rawData + System.lineSeparator();

            // 追加写入文件
            try (FileWriter writer = new FileWriter(file, true)) {
                writer.write(content);
                writer.flush();
            }

            logger.debug("原始数据已保存到文件: {}", file.getAbsolutePath());

        } catch (IOException e) {
            logger.error("保存设备[{}]原始数据到文件失败", device.getCode(), e);
        } catch (Exception e) {
            logger.error("保存设备[{}]原始数据时发生异常", device.getCode(), e);
        }
    }

    /**
     * 清理过期的设备原始数据文件
     * 删除创建时间超过指定保留天数的文件
     *
     * @param retentionDays 要保留的天数
     * @return 删除的文件数量
     */
    public static int cleanupOldData(int retentionDays) {
        if (retentionDays <= 0) {
            logger.warn("保留天数必须大于0，当前值: {}", retentionDays);
            return 0;
        }

        int deletedCount = 0;

        try {
            // 计算截止日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -retentionDays);
            Date cutoffDate = calendar.getTime();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            String cutoffDateStr = dateFormat.format(cutoffDate);

            // 构建设备原始数据根目录路径
            String basePath = SnctConfig.getProfile();
            String rootDirPath = File.separator + "snct" + File.separator + "device_raw_data";
            File rootDir = new File(rootDirPath);

            if (!rootDir.exists() || !rootDir.isDirectory()) {
                logger.debug("设备原始数据根目录不存在: {}", rootDirPath);
                return 0;
            }

            // 遍历日期目录
            File[] dateDirs = rootDir.listFiles();
            if (dateDirs != null) {
                for (File dateDir : dateDirs) {
                    if (dateDir.isDirectory()) {
                        String dirName = dateDir.getName();
                        // 检查目录名是否为日期格式且早于截止日期
                        if (dirName.matches("\\d{8}") && dirName.compareTo(cutoffDateStr) < 0) {
                            deletedCount += deleteDirectoryRecursively(dateDir);
                        }
                    }
                }
            }

            logger.info("清理过期设备原始数据完成，删除文件数量: {}, 保留天数: {}", deletedCount, retentionDays);

        } catch (Exception e) {
            logger.error("清理过期设备原始数据时发生异常，保留天数: {}", retentionDays, e);
        }

        return deletedCount;
    }

    /**
     * 递归删除目录及其所有文件
     *
     * @param dir 要删除的目录
     * @return 删除的文件数量
     */
    private static int deleteDirectoryRecursively(File dir) {
        int deletedCount = 0;

        try {
            if (dir.exists()) {
                File[] files = dir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file.isDirectory()) {
                            deletedCount += deleteDirectoryRecursively(file);
                        } else {
                            if (file.delete()) {
                                deletedCount++;
                                logger.debug("删除文件: {}", file.getAbsolutePath());
                            } else {
                                logger.warn("删除文件失败: {}", file.getAbsolutePath());
                            }
                        }
                    }
                }

                // 删除空目录
                if (dir.delete()) {
                    logger.debug("删除目录: {}", dir.getAbsolutePath());
                } else {
                    logger.warn("删除目录失败: {}", dir.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            logger.error("删除目录时发生异常: {}", dir.getAbsolutePath(), e);
        }

        return deletedCount;
    }
}
